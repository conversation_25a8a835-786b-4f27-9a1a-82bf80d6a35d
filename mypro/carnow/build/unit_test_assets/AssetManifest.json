{".env": [".env"], "assets/data/automotive_reference_data.json": ["assets/data/automotive_reference_data.json"], "assets/data/body_styles.json": ["assets/data/body_styles.json"], "assets/data/brake_systems.json": ["assets/data/brake_systems.json"], "assets/data/trailer_types.json": ["assets/data/trailer_types.json"], "assets/data/transmission_types.json": ["assets/data/transmission_types.json"], "assets/data/valvetrain_designs.json": ["assets/data/valvetrain_designs.json"], "assets/icons/google_logo.svg": ["assets/icons/google_logo.svg"], "assets/images/logo.png": ["assets/images/logo.png"], "assets/images/placeholder.png": ["assets/images/placeholder.png"], "fonts/Cairo/Cairo-200.ttf": ["fonts/Cairo/Cairo-200.ttf"], "fonts/Cairo/Cairo-300.ttf": ["fonts/Cairo/Cairo-300.ttf"], "fonts/Cairo/Cairo-500.ttf": ["fonts/Cairo/Cairo-500.ttf"], "fonts/Cairo/Cairo-600.ttf": ["fonts/Cairo/Cairo-600.ttf"], "fonts/Cairo/Cairo-700.ttf": ["fonts/Cairo/Cairo-700.ttf"], "fonts/Cairo/Cairo-800.ttf": ["fonts/Cairo/Cairo-800.ttf"], "fonts/Cairo/Cairo-900.ttf": ["fonts/Cairo/Cairo-900.ttf"], "fonts/Cairo/Cairo-regular.ttf": ["fonts/Cairo/Cairo-regular.ttf"], "packages/flutter_vector_icons/fonts/AntDesign.ttf": ["packages/flutter_vector_icons/fonts/AntDesign.ttf"], "packages/flutter_vector_icons/fonts/Entypo.ttf": ["packages/flutter_vector_icons/fonts/Entypo.ttf"], "packages/flutter_vector_icons/fonts/EvilIcons.ttf": ["packages/flutter_vector_icons/fonts/EvilIcons.ttf"], "packages/flutter_vector_icons/fonts/Feather.ttf": ["packages/flutter_vector_icons/fonts/Feather.ttf"], "packages/flutter_vector_icons/fonts/FontAwesome.ttf": ["packages/flutter_vector_icons/fonts/FontAwesome.ttf"], "packages/flutter_vector_icons/fonts/FontAwesome5_Brands.ttf": ["packages/flutter_vector_icons/fonts/FontAwesome5_Brands.ttf"], "packages/flutter_vector_icons/fonts/FontAwesome5_Regular.ttf": ["packages/flutter_vector_icons/fonts/FontAwesome5_Regular.ttf"], "packages/flutter_vector_icons/fonts/FontAwesome5_Solid.ttf": ["packages/flutter_vector_icons/fonts/FontAwesome5_Solid.ttf"], "packages/flutter_vector_icons/fonts/Fontisto.ttf": ["packages/flutter_vector_icons/fonts/Fontisto.ttf"], "packages/flutter_vector_icons/fonts/Foundation.ttf": ["packages/flutter_vector_icons/fonts/Foundation.ttf"], "packages/flutter_vector_icons/fonts/Ionicons.ttf": ["packages/flutter_vector_icons/fonts/Ionicons.ttf"], "packages/flutter_vector_icons/fonts/MaterialCommunityIcons.ttf": ["packages/flutter_vector_icons/fonts/MaterialCommunityIcons.ttf"], "packages/flutter_vector_icons/fonts/MaterialIcons.ttf": ["packages/flutter_vector_icons/fonts/MaterialIcons.ttf"], "packages/flutter_vector_icons/fonts/Octicons.ttf": ["packages/flutter_vector_icons/fonts/Octicons.ttf"], "packages/flutter_vector_icons/fonts/SimpleLineIcons.ttf": ["packages/flutter_vector_icons/fonts/SimpleLineIcons.ttf"], "packages/flutter_vector_icons/fonts/Zocial.ttf": ["packages/flutter_vector_icons/fonts/Zocial.ttf"]}