// =============================================================================
// CarNow Test Data for Testing ONLY
// بيانات اختبار CarNow للاختبارات فقط
// =============================================================================
// 
// ⚠️  WARNING: This file contains TEST DATA for TESTING ONLY
// ⚠️  تحذير: هذا الملف يحتوي على بيانات اختبار للاختبارات فقط
// 
// ❌ NEVER use this data in production
// ❌ لا تستخدم هذه البيانات في الإنتاج أبداً
// 
// ✅ ONLY use for unit tests, widget tests, and integration tests
// ✅ استخدم فقط لاختبارات الوحدة والمكونات والتكامل
// 
// 🔒 Forever Plan Compliance: Real data from Supabase only in production
// 🔒 الالتزام بخطة للأبد: بيانات حقيقية من Supabase فقط في الإنتاج
// =============================================================================

/// Test data factory for testing purposes only
/// مصنع بيانات الاختبار لأغراض الاختبار فقط
class TestDataFactory {
  static const String _testPrefix = 'TEST_';
  static const String _mockPrefix = 'MOCK_';

  /// Generate test user data for testing
  /// إنشاء بيانات مستخدم اختبار للاختبار
  static Map<String, dynamic> createTestUser({
    String? id,
    String? email,
    String? name,
    String? phone,
  }) {
    return {
      'id': id ?? '${_testPrefix}USER_${DateTime.now().millisecondsSinceEpoch}',
      'email': email ?? '${_mockPrefix}<EMAIL>',
      'name': name ?? '${_mockPrefix}Test User',
      'phone': phone ?? '+966501234567',
      'role': 'customer',
      'is_verified': true,
      'created_at': DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Generate test product data for testing
  /// إنشاء بيانات منتج اختبار للاختبار
  static Map<String, dynamic> createTestProduct({
    String? id,
    String? name,
    double price = 99.99,
    int stockQuantity = 10,
    String? categoryId,
  }) {
    return {
      'id': id ?? '${_testPrefix}PRODUCT_${DateTime.now().millisecondsSinceEpoch}',
      'name': name ?? '${_mockPrefix}Test Car Part',
      'description': '${_mockPrefix}High quality car part for testing',
      'price': price,
      'stock_quantity': stockQuantity,
      'category_id': categoryId ?? '${_testPrefix}CATEGORY_123',
      'brand': '${_mockPrefix}Test Brand',
      'model': '${_mockPrefix}Test Model',
      'year': 2023,
      'condition': 'new',
      'images': ['/test/images/test_product.jpg'],
      'is_active': true,
      'created_at': DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test cart data
  static Map<String, dynamic> createTestCart({
    String? id,
    String? userId,
    bool isEmpty = false,
  }) {
    return {
      'id': id ?? 'TEST_CART_${DateTime.now().millisecondsSinceEpoch}',
      'user_id': userId ?? 'TEST_USER_123',
      'items': isEmpty ? [] : [],
      'total': isEmpty ? 0.0 : 100.0,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test cart item data
  static Map<String, dynamic> createTestCartItem({
    String? id,
    String? cartId,
    String? productId,
    int quantity = 1,
    double price = 50.0,
  }) {
    return {
      'id': id ?? 'TEST_CART_ITEM_${DateTime.now().millisecondsSinceEpoch}',
      'cart_id': cartId ?? 'TEST_CART_123',
      'product_id': productId ?? 'TEST_PRODUCT_123',
      'quantity': quantity,
      'price': price,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test order data
  static Map<String, dynamic> createTestOrder({
    String? id,
    String? userId,
    double totalAmount = 100.0,
    String status = 'confirmed',
    String paymentStatus = 'paid',
  }) {
    return {
      'id': id ?? 'TEST_ORDER_${DateTime.now().millisecondsSinceEpoch}',
      'user_id': userId ?? 'TEST_USER_123',
      'total_amount': totalAmount,
      'status': status,
      'payment_status': paymentStatus,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test order item data
  static Map<String, dynamic> createTestOrderItem({
    String? id,
    String? orderId,
    String? productId,
    int quantity = 1,
    double price = 50.0,
  }) {
    return {
      'id': id ?? 'TEST_ORDER_ITEM_${DateTime.now().millisecondsSinceEpoch}',
      'order_id': orderId ?? 'TEST_ORDER_123',
      'product_id': productId ?? 'TEST_PRODUCT_123',
      'quantity': quantity,
      'price': price,
      'created_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test payment data
  static Map<String, dynamic> createTestPayment({
    String? id,
    String? orderId,
    String? paymentIntentId,
    double amount = 100.0,
    String paymentMethod = 'wallet',
    String status = 'completed',
  }) {
    return {
      'id': id ?? 'TEST_PAYMENT_${DateTime.now().millisecondsSinceEpoch}',
      'order_id': orderId ?? 'TEST_ORDER_123',
      'payment_intent_id': paymentIntentId ?? 'TEST_INTENT_123',
      'amount': amount,
      'payment_method': paymentMethod,
      'status': status,
      'created_at': DateTime.now().toIso8601String(),
      'completed_at': status == 'completed' ? DateTime.now().toIso8601String() : null,
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test payment intent data
  static Map<String, dynamic> createTestPaymentIntent({
    String? id,
    String? orderId,
    double amount = 100.0,
    String currency = 'LYD',
    String status = 'requires_payment_method',
  }) {
    return {
      'id': id ?? 'TEST_INTENT_${DateTime.now().millisecondsSinceEpoch}',
      'order_id': orderId ?? 'TEST_ORDER_123',
      'amount': amount,
      'currency': currency,
      'status': status,
      'created_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test wallet data
  static Map<String, dynamic> createTestWallet({
    String? id,
    String? userId,
    double balance = 1000.0,
  }) {
    return {
      'id': id ?? 'TEST_WALLET_${DateTime.now().millisecondsSinceEpoch}',
      'user_id': userId ?? 'TEST_USER_123',
      'balance': balance,
      'currency': 'LYD',
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test card details data
  static Map<String, dynamic> createTestCardDetails({
    String? id,
    String lastFour = '1234',
    String brand = 'visa',
  }) {
    return {
      'id': id ?? 'TEST_CARD_${DateTime.now().millisecondsSinceEpoch}',
      'last_four': lastFour,
      'brand': brand,
      'exp_month': 12,
      'exp_year': 2025,
      'number': '**** **** **** $lastFour',
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test refund data
  static Map<String, dynamic> createTestRefund({
    String? id,
    String? paymentId,
    double amount = 50.0,
    String reason = 'customer_request',
    String status = 'pending',
  }) {
    return {
      'id': id ?? 'TEST_REFUND_${DateTime.now().millisecondsSinceEpoch}',
      'payment_id': paymentId ?? 'TEST_PAYMENT_123',
      'amount': amount,
      'reason': reason,
      'status': status,
      'created_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test notification data
  static Map<String, dynamic> createTestNotification({
    String? id,
    String? userId,
    String type = 'order_confirmation',
    String message = 'MOCK_Test notification',
  }) {
    return {
      'id': id ?? 'TEST_NOTIFICATION_${DateTime.now().millisecondsSinceEpoch}',
      'user_id': userId ?? 'TEST_USER_123',
      'type': type,
      'message': message,
      'is_read': false,
      'created_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test WebSocket message data
  static Map<String, dynamic> createTestWebSocketMessage({
    String type = 'cart',
    String event = 'updated',
    Map<String, dynamic>? data,
  }) {
    return {
      'type': type,
      'event': event,
      'data': data ?? {'test': true},
      'timestamp': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test image data
  static Map<String, dynamic> createTestImageData({
    String filename = 'test_image.jpg',
    String mimeType = 'image/jpeg',
    int size = 1024000,
  }) {
    return {
      'filename': filename,
      'mime_type': mimeType,
      'size': size,
      'data': 'MOCK_IMAGE_DATA_BASE64',
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Create test processed image data
  static Map<String, dynamic> createTestProcessedImage({
    String? id,
    String originalName = 'test_image.jpg',
    String? productId,
    double compressionRatio = 0.8,
    int quality = 85,
  }) {
    return {
      'id': id ?? 'TEST_IMAGE_${DateTime.now().millisecondsSinceEpoch}',
      'original_filename': originalName,
      'product_id': productId,
      'original_url': '/api/v1/images/TEST_IMAGE_123/original.jpg',
      'formats': {
        'jpeg': '/api/v1/images/TEST_IMAGE_123/optimized.jpg',
        'webp': '/api/v1/images/TEST_IMAGE_123/optimized.webp',
      },
      'sizes': {
        'thumbnail': '/api/v1/images/TEST_IMAGE_123/thumbnail.jpg',
        'small': '/api/v1/images/TEST_IMAGE_123/small.jpg',
        'medium': '/api/v1/images/TEST_IMAGE_123/medium.jpg',
        'large': '/api/v1/images/TEST_IMAGE_123/large.jpg',
      },
      'metadata': {
        'compression_ratio': compressionRatio,
        'quality': quality,
        'optimized': true,
        'width': 1200,
        'height': 800,
      },
      'created_at': DateTime.now().toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }
}

/// Test Data Validator (Carefully Controlled)
///
/// This validator ensures test data is properly marked and used only in test environment
class TestDataValidator {
  /// Validate that data is properly marked as test data
  static void validateTestDataOnly(Map<String, dynamic> data) {
    if (!data.containsKey('test_environment') || data['test_environment'] != true) {
      throw StateError('Invalid test data - missing test_environment indicator');
    }

    if (!data.containsKey('test_data_indicator') || data['test_data_indicator'] != true) {
      throw StateError('Invalid test data - missing test_data_indicator');
    }
  }

  /// Ensure we're running in test environment
  static void ensureTestEnvironment() {
    if (!const bool.fromEnvironment('FLUTTER_TEST')) {
      throw StateError('Test data can only be used in test environment');
    }
  }
}

/// Test data scenarios for different test cases
class TestDataScenarios {
  /// Empty data scenario for testing empty states
  static Map<String, dynamic> get emptyData => {
    'data': [],
    'total': 0,
    'testData': true,
    'testEnvironment': true,
  };

  /// Error data scenario for testing error handling
  static Map<String, dynamic> get errorData => {
    'error': 'MOCK_Test error occurred',
    'code': 'TEST_ERROR',
    'testData': true,
    'testEnvironment': true,
  };
}
