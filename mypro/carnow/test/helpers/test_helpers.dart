// =============================================================================
// CarNow Test Helpers
// مساعدات اختبارات CarNow
// =============================================================================
// 
// ⚠️  WARNING: This file contains TEST HELPERS for TESTING ONLY
// ⚠️  تحذير: هذا الملف يحتوي على مساعدات اختبار للاختبارات فقط
// 
// ❌ NEVER use this in production
// ❌ لا تستخدم هذا في الإنتاج أبداً
// 
// ✅ ONLY use for unit tests, widget tests, and integration tests
// ✅ استخدم فقط لاختبارات الوحدة والمكونات والتكامل
// 
// 🔒 Forever Plan Compliance: Real data from Supabase only in production
// 🔒 الالتزام بخطة للأبد: بيانات حقيقية من Supabase فقط في الإنتاج
// =============================================================================

import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../config/test_config.dart';

/// Test helpers for CarNow application
/// مساعدات الاختبار لتطبيق CarNow
class TestHelpers {
  static bool _isInitialized = false;

  /// Setup test environment
  /// إعداد بيئة الاختبار
  static void setupTestEnvironment() {
    if (_isInitialized) return;

    // Set up test configuration
    TestConfig.initialize();

    // Configure test timeouts
    TestWidgetsFlutterBinding.ensureInitialized();
    TestWidgetsFlutterBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/connectivity'),
      (MethodCall methodCall) async {
        return 'wifi';
      },
    );

    // Set up logging for tests
    if (kDebugMode) {
      print('🧪 Test environment initialized');
      print('   Platform: ${Platform.operatingSystem}');
    }

    _isInitialized = true;
  }

  /// Clean up test environment
  /// تنظيف بيئة الاختبار
  static void cleanupTestEnvironment() {
    // Reset any global state
    TestWidgetsFlutterBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/connectivity'),
      null,
    );

    if (kDebugMode) {
      print('🧹 Test environment cleaned up');
    }
  }

  /// Create a test timeout
  /// إنشاء مهلة زمنية للاختبار
  static Duration getTestTimeout({Duration? customTimeout}) {
    return customTimeout ?? TestConfig.defaultTimeout;
  }

  /// Wait for async operations to complete
  /// انتظار انتهاء العمليات غير المتزامنة
  static Future<void> waitForAsyncOperations() async {
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Create a mock response for testing
  /// إنشاء استجابة وهمية للاختبار
  static Map<String, dynamic> createMockResponse({
    bool success = true,
    dynamic data,
    String? message,
    int statusCode = 200,
  }) {
    return {
      'success': success,
      if (data != null) 'data': data,
      if (message != null) 'message': message,
      'status_code': statusCode,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Create a mock error response for testing
  /// إنشاء استجابة خطأ وهمية للاختبار
  static Map<String, dynamic> createMockErrorResponse({
    String error = 'Test error',
    String code = 'TEST_ERROR',
    int statusCode = 400,
  }) {
    return {
      'success': false,
      'error': error,
      'code': code,
      'status_code': statusCode,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Validate test data compliance
  /// التحقق من امتثال بيانات الاختبار
  static void validateTestDataCompliance(Map<String, dynamic> data) {
    // Ensure test data has proper indicators
    if (!data.containsKey('test_environment') || !data.containsKey('test_data_indicator')) {
      throw StateError('Test data must contain proper indicators');
    }

    // Ensure test data is marked as test data
    if (data['test_environment'] != true || data['test_data_indicator'] != true) {
      throw StateError('Test data must be properly marked as test data');
    }
  }

  /// Check if running in test environment
  /// التحقق من تشغيل البيئة في وضع الاختبار
  static bool isTestEnvironment() {
    return const bool.fromEnvironment('FLUTTER_TEST') || 
           const bool.fromEnvironment('INTEGRATION_TEST');
  }

  /// Ensure test environment is active
  /// التأكد من نشاط بيئة الاختبار
  static void ensureTestEnvironment() {
    if (!isTestEnvironment()) {
      throw StateError('Test helpers can only be used in test environment');
    }
  }

  /// Create a test user agent string
  /// إنشاء سلسلة وكيل مستخدم للاختبار
  static String createTestUserAgent() {
    return 'CarNow-Test-Client/1.0.0 (Test Environment)';
  }

  /// Create test headers
  /// إنشاء رؤوس HTTP للاختبار
  static Map<String, String> createTestHeaders({
    String? authorization,
    String? contentType = 'application/json',
  }) {
    final headers = <String, String>{
      'User-Agent': createTestUserAgent(),
      'X-Test-Environment': 'true',
      'X-Test-Timestamp': DateTime.now().toIso8601String(),
    };

    if (contentType != null) {
      headers['Content-Type'] = contentType;
    }

    if (authorization != null) {
      headers['Authorization'] = authorization;
    }

    return headers;
  }

  /// Mock network connectivity
  /// محاكاة اتصال الشبكة
  static void mockNetworkConnectivity({bool isConnected = true}) {
    TestWidgetsFlutterBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/connectivity'),
      (MethodCall methodCall) async {
        return isConnected ? 'wifi' : 'none';
      },
    );
  }

  /// Reset all mocks
  /// إعادة تعيين جميع المحاكيات
  static void resetAllMocks() {
    resetMocktailState();
  }

  /// Create a test delay
  /// إنشاء تأخير للاختبار
  static Future<void> testDelay([Duration? duration]) async {
    await Future.delayed(duration ?? const Duration(milliseconds: 50));
  }

  /// Validate test file structure
  /// التحقق من صحة هيكل ملف الاختبار
  static void validateTestFileStructure() {
    ensureTestEnvironment();
    
    // Check if test helpers are properly imported
    if (!_isInitialized) {
      throw StateError('Test environment not initialized. Call TestHelpers.setupTestEnvironment() first.');
    }
  }

  /// Create test session data
  /// إنشاء بيانات جلسة الاختبار
  static Map<String, dynamic> createTestSessionData({
    String? sessionId,
    String? userId,
    DateTime? createdAt,
  }) {
    return {
      'session_id': sessionId ?? 'TEST_SESSION_${DateTime.now().millisecondsSinceEpoch}',
      'user_id': userId ?? 'TEST_USER_${DateTime.now().millisecondsSinceEpoch}',
      'created_at': (createdAt ?? DateTime.now()).toIso8601String(),
      'test_environment': true,
      'test_data_indicator': true,
    };
  }

  /// Validate test session data
  /// التحقق من صحة بيانات جلسة الاختبار
  static void validateTestSessionData(Map<String, dynamic> sessionData) {
    validateTestDataCompliance(sessionData);
    
    if (!sessionData.containsKey('session_id') || 
        !sessionData.containsKey('user_id') ||
        !sessionData.containsKey('created_at')) {
      throw StateError('Test session data must contain session_id, user_id, and created_at');
    }
  }
} 