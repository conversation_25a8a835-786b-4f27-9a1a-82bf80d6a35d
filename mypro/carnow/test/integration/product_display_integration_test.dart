import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'dart:async';
import 'package:patrol/patrol.dart';
import 'package:carnow/main.dart' as app;

const bool _isCI = bool.fromEnvironment('CI');

void main() {
  // Only use patrol setup when running integration tests
  if (const bool.fromEnvironment('INTEGRATION_TEST', defaultValue: false)) {
    patrolSetUp(() {
      // Patrol setup for integration tests
    });
  }

  group('CarNow Public Access Integration Tests', () {
    testWidgets(
      'Guest users can view categories and products',
      (tester) async {
        // Start the app
        unawaited(app.main());
        await tester.pumpAndSettle();

        // Wait for app initialization
        await tester.pumpAndSettle(const Duration(seconds: 3));

        // Verify app loads without requiring authentication
        expect(find.byType(MaterialApp), findsOneWidget);

        // Check if categories are visible
        await tester.tap(find.text('الفئات'));
        await tester.pumpAndSettle();

        // Should see category items
        expect(find.byType(ListView), findsAtLeastNWidgets(1));

        // Go back to home
        await tester.tap(find.text('الرئيسية'));
        await tester.pumpAndSettle();

        // Check if products are loading/visible on home screen
        // Products should be visible even without login
        expect(find.byType(RefreshIndicator), findsAtLeastNWidgets(1));
      },
      skip: _isCI,
    );

    testWidgets(
      'Guest users are redirected to login only for protected actions',
      (tester) async {
        unawaited(app.main());
        await tester.pumpAndSettle();

        // Wait for app initialization
        await tester.pumpAndSettle(const Duration(seconds: 3));

        // Try to access account - should redirect to login
        await tester.tap(find.text('الحساب'));
        await tester.pumpAndSettle();

        // Should be redirected to login
        expect(find.text('تسجيل الدخول'), findsAtLeastNWidgets(1));
      },
    );

    testWidgets('Navigation works correctly for public sections', (
      tester,
    ) async {
      unawaited(app.main());
      await tester.pumpAndSettle();

      // Wait for app initialization
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Test navigation between public sections
      final publicSections = ['الرئيسية', 'الفئات', 'الإشعارات'];

      for (final section in publicSections) {
        if (find.text(section).evaluate().isNotEmpty) {
          await tester.tap(find.text(section));
          await tester.pumpAndSettle();

          // Should navigate successfully without authentication
          expect(find.byType(Scaffold), findsAtLeastNWidgets(1));
        }
      }
    });

    testWidgets('App handles network errors gracefully', (tester) async {
      unawaited(app.main());
      await tester.pumpAndSettle();

      // Wait for app initialization
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // App should handle network errors without crashing
      expect(find.byType(MaterialApp), findsOneWidget);

      // Should not show critical error screens on startup
      expect(find.text('خطأ فادح'), findsNothing);
      expect(find.text('Critical Error'), findsNothing);
    });

    testWidgets('Search functionality works without authentication', (
      tester,
    ) async {
      unawaited(app.main());
      await tester.pumpAndSettle();

      // Wait for app initialization
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Look for search functionality
      final searchIcon = find.byIcon(Icons.search);
      if (searchIcon.evaluate().isNotEmpty) {
        await tester.tap(searchIcon);
        await tester.pumpAndSettle();

        // Search should work without requiring login
        expect(find.byType(TextField), findsAtLeastNWidgets(1));
      }
    });
  }, skip: _isCI);
}
